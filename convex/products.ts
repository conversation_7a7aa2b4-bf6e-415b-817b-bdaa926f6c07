import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { productStatsAggregate, productStockAggregate } from "./aggregates";
import { internal } from "./_generated/api";

// Query to get all products with pagination (PERFORMANCE OPTIMIZED)
export const getProducts = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    supplierId: v.optional(v.id("suppliers"))
  },
  handler: async (ctx, { paginationOpts, status, supplierId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    let query;

    // Use indexes for efficient filtering
    if (status) {
      query = ctx.db.query("products").withIndex("by_status", (q) => q.eq("status", status));
    } else if (supplierId) {
      query = ctx.db.query("products").withIndex("by_supplier", (q) => q.eq("supplierId", supplierId));
    } else {
      query = ctx.db.query("products");
    }

    // NOTE: The N+1 problem is solved by denormalizing supplier data. No extra fetches needed.
    if (paginationOpts) {
      return await query.paginate(paginationOpts);
    }

    return await query.collect();
  },
});

// Query to get a single product by ID (PERFORMANCE OPTIMIZED)
export const getProduct = query({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Fetch user data, supplier data is already denormalized
    const createdByUserDoc = await ctx.db.get(product.createdBy);
    const updatedByUserDoc = await ctx.db.get(product.updatedBy);

    // Safely construct user info, assuming standard auth table schema
    const createdByUser = createdByUserDoc
      ? {
          name: [createdByUserDoc.givenName, createdByUserDoc.familyName]
            .filter(Boolean)
            .join(" ") || createdByUserDoc.email, // Fallback to email
          email: createdByUserDoc.email,
        }
      : null;

    const updatedByUser = updatedByUserDoc
      ? {
          name: [updatedByUserDoc.givenName, updatedByUserDoc.familyName]
            .filter(Boolean)
            .join(" ") || updatedByUserDoc.email, // Fallback to email
          email: updatedByUserDoc.email,
        }
      : null;

    return {
      ...product,
      createdByUser,
      updatedByUser,
    };
  },
});

// Mutation to create a new product (PERFORMANCE OPTIMIZED)
export const createProduct = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    stockCount: v.number(),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    providerData: v.object({
      source: v.string(),
      productUrl: v.string(),
      providerId: v.string(),
      lastScraped: v.number(),
      providerSpecificData: v.optional(v.any()),
    }),
    pricingTiers: v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
    })),
    variants: v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.union(v.literal('modifier'), v.literal('absolute')),
      priceModifier: v.optional(v.number()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    })),
    customServices: v.array(v.object({
      name: v.string(),
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      isRequired: v.boolean(),
    })),
    attributes: v.array(v.object({
      name: v.string(),
      value: v.any(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);

    const userId = await auth.getUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier) throw new Error("Supplier not found");

    if (args.priceInYuan <= 0 || args.finalPrice <= 0) throw new Error("Prices must be positive");
    if (args.stockCount < 0) throw new Error("Stock count cannot be negative");
    if (!args.title.trim() || !args.description.trim()) throw new Error("Title and description are required");

    // Denormalize supplier data
    const denormalizedSupplier = { id: supplier._id, name: supplier.name };

    const productId = await ctx.db.insert("products", {
      ...args,
      supplier: denormalizedSupplier,
      status: args.status || "active",
      createdBy: userId,
      updatedBy: userId,
    });

    // Update unique tags collection
    await ctx.scheduler.runAfter(0, internal.tags.addTags, { tags: args.tags });

    // Update aggregates
    const product = await ctx.db.get(productId);
    if (product) {
      await productStatsAggregate.insert(ctx, product);
      await productStockAggregate.insert(ctx, product);
    }

    return productId;
  },
});

// Mutation to update a product (PERFORMANCE OPTIMIZED)
export const updateProduct = mutation({
  args: {
    id: v.id("products"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    curationNotes: v.optional(v.string()),
    supplierId: v.optional(v.id("suppliers")),
    priceInYuan: v.optional(v.number()),
    serviceFee: v.optional(v.number()),
    finalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),
    stockCount: v.optional(v.number()),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    providerData: v.optional(v.object({
      source: v.string(),
      productUrl: v.string(),
      providerId: v.string(),
      lastScraped: v.number(),
      providerSpecificData: v.optional(v.any()),
    })),
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
    }))),
    variants: v.optional(v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.union(v.literal('modifier'), v.literal('absolute')),
      priceModifier: v.optional(v.number()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    }))),
    customServices: v.optional(v.array(v.object({
      name: v.string(),
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      isRequired: v.boolean(),
    }))),
    attributes: v.optional(v.array(v.object({
      name: v.string(),
      value: v.any(),
    }))),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const existingProduct = await ctx.db.get(id);
    if (!existingProduct) throw new Error("Product not found");

    let denormalizedSupplier = existingProduct.supplier;
    if (updates.supplierId && updates.supplierId !== existingProduct.supplierId) {
      const newSupplier = await ctx.db.get(updates.supplierId);
      if (!newSupplier) throw new Error("Supplier not found");
      denormalizedSupplier = { id: newSupplier._id, name: newSupplier.name };
    }

    await ctx.db.patch(id, {
      ...updates,
      supplier: denormalizedSupplier,
      updatedBy: userId,
    });

    if (updates.tags) {
      await ctx.scheduler.runAfter(0, internal.tags.addTags, { tags: updates.tags });
    }

    // Update aggregates
    const updatedProduct = await ctx.db.get(id);
    if (updatedProduct) {
      await productStatsAggregate.replace(ctx, existingProduct, updatedProduct);
      await productStockAggregate.replace(ctx, existingProduct, updatedProduct);
    }

    return { success: true };
  },
});

// Mutation to delete a product (soft delete)
export const deleteProduct = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_DELETE);
    const userId = await auth.getUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const product = await ctx.db.get(id);
    if (!product) throw new Error("Product not found");

    await ctx.db.patch(id, { status: "archived", updatedBy: userId });

    const updatedProduct = await ctx.db.get(id);
    if (updatedProduct) {
      await productStatsAggregate.replace(ctx, product, updatedProduct);
      await productStockAggregate.replace(ctx, product, updatedProduct);
    }

    return { success: true };
  },
});

// Query to get product statistics using efficient aggregates
export const getProductStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);
    // This was already efficient, no changes needed.
    const total = await productStatsAggregate.count(ctx);
    const active = await productStatsAggregate.count(ctx, { bounds: { lower: { key: "active", inclusive: true }, upper: { key: "active", inclusive: true } } });
    const inactive = await productStatsAggregate.count(ctx, { bounds: { lower: { key: "inactive", inclusive: true }, upper: { key: "inactive", inclusive: true } } });
    const archived = await productStatsAggregate.count(ctx, { bounds: { lower: { key: "archived", inclusive: true }, upper: { key: "archived", inclusive: true } } });
    const lowStock = await productStockAggregate.count(ctx, { bounds: { upper: { key: 9, inclusive: true } } });
    const outOfStock = await productStockAggregate.count(ctx, { bounds: { lower: { key: 0, inclusive: true }, upper: { key: 0, inclusive: true } } });

    return { total, active, inactive, archived, lowStock, outOfStock };
  },
});

// Query to get low stock products (PERFORMANCE OPTIMIZED)
export const getLowStockProducts = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, { limit = 10 }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    // Use the index on stockCount for efficient querying
    return await ctx.db
      .query("products")
      .withIndex("by_stockCount", q => q.gt(0).lt(11))
      .take(limit);
  },
});

// Mutation to update product with generated embedding
export const updateProductEmbedding = mutation({
  args: {
    productId: v.id("products"),
    embedding: v.array(v.float64()),
  },
  handler: async (ctx, { productId, embedding }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);
    const product = await ctx.db.get(productId);
    if (!product) throw new Error("Product not found");

    await ctx.db.patch(productId, { imageEmbedding: embedding });
    return { success: true };
  },
});

// Query to get product images for embedding generation
export const getProductImagesForEmbedding = query({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);
    const product = await ctx.db.get(productId);
    if (!product) throw new Error("Product not found");

    const imageUrls = (await Promise.all(product.images.map(async (imageId) => {
      if (typeof imageId !== "string") {
        return await ctx.storage.getUrl(imageId);
      }
      return imageId;
    }))).filter((url): url is string => url !== null);

    return { productId, imageUrls, hasEmbedding: !!product.imageEmbedding };
  },
});

// Advanced search query (PERFORMANCE OPTIMIZED)
export const advancedSearchProducts = query({
  args: {
    searchTerm: v.optional(v.string()),
    // Other filters from original query can be added here
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    if (!args.searchTerm || args.searchTerm.trim() === "") {
      // Return empty or handle as a generic query if no search term
      return [];
    }

    // Use the search index for efficient text search
    const products = await ctx.db
      .query("products")
      .withSearchIndex("search_all", (q) => q.search("title", args.searchTerm!))
      .take(args.limit || 20);

    return products;
  },
});

// Search products by image similarity (PERFORMANCE OPTIMIZED)
export const searchProductsByImage = query({
  args: {
    targetEmbedding: v.array(v.float64()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { targetEmbedding, limit = 10 }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    // Use the vector index for efficient similarity search
    const products = await ctx.db
      .query("products")
      .withVectorIndex("by_embedding", q => q.vector(targetEmbedding))
      .take(limit);

    return products;
  },
});

// Query to get all unique tags from the dedicated table (PERFORMANCE OPTIMIZED)
export const getUniqueTags = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);
    const tags = await ctx.db.query("uniqueTags").collect();
    return tags.map((t) => t.tag);
  },
});
