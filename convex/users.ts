import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get all users with pagination and filtering
export const getUsers = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    search: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, { paginationOpts, search }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_VIEW);

    // Build query
    const query = ctx.db.query("users");

    // Apply pagination if provided
    if (paginationOpts) {
      const results = await query.paginate(paginationOpts);
      
      // Filter and enrich users
      let filteredUsers = results.page;
      
      if (search) {
        const searchLower = search.toLowerCase();
        filteredUsers = filteredUsers.filter(user => 
          user.name?.toLowerCase().includes(searchLower) ||
          user.email?.toLowerCase().includes(searchLower)
        );
      }

      // Get admin status for each user
      const enrichedUsers = await Promise.all(
        filteredUsers.map(async (user) => {
          const adminUser = await ctx.db
            .query("adminUsers")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .first();

          // Get user's orders count
          const ordersCount = await ctx.db
            .query("orders")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .collect()
            .then(orders => orders.length);

          return {
            ...user,
            isAdmin: !!adminUser,
            adminRole: adminUser?.role || null,
            isAdminActive: adminUser?.isActive || false,
            ordersCount,
            lastLoginAt: adminUser?.lastLoginAt || null,
          };
        })
      );

      return {
        ...results,
        page: enrichedUsers,
      };
    }

    // Get all users without pagination
    const users = await query.collect();
    
    // Apply filters
    let filteredUsers = users;
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower)
      );
    }

    // Enrich with admin status and stats
    const enrichedUsers = await Promise.all(
      filteredUsers.map(async (user) => {
        const adminUser = await ctx.db
          .query("adminUsers")
          .withIndex("by_user", (q) => q.eq("userId", user._id))
          .first();

        const ordersCount = await ctx.db
          .query("orders")
          .withIndex("by_user", (q) => q.eq("userId", user._id))
          .collect()
          .then(orders => orders.length);

        return {
          ...user,
          isAdmin: !!adminUser,
          adminRole: adminUser?.role || null,
          isAdminActive: adminUser?.isActive || false,
          ordersCount,
          lastLoginAt: adminUser?.lastLoginAt || null,
        };
      })
    );

    return enrichedUsers;
  },
});

// Query to get a single user by ID
export const getUser = query({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_VIEW);

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Get admin status
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();

    // Get user's orders
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .collect();

    // Get user's created products
    const createdProducts = await ctx.db
      .query("products")
      .withIndex("by_created_by", (q) => q.eq("createdBy", id))
      .collect();

    return {
      ...user,
      isAdmin: !!adminUser,
      adminRole: adminUser?.role || null,
      isAdminActive: adminUser?.isActive || false,
      lastLoginAt: adminUser?.lastLoginAt || null,
      orders: orders.map(order => ({
        id: order._id,
        status: order.status,
        totalAmount: order.totalAmount,
        createdAt: order._creationTime,
      })),
      createdProducts: createdProducts.map(product => ({
        id: product._id,
        title: product.title,
        status: product.status,
        finalPrice: product.finalPrice,
      })),
      stats: {
        totalOrders: orders.length,
        totalSpent: orders.reduce((sum, order) => sum + order.totalAmount, 0),
        productsCreated: createdProducts.length,
      },
    };
  },
});

// Query to check if a user is an active admin
export const isUserAdmin = query({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();
    return !!(adminUser && adminUser.isActive);
  },
});

// Query to get user statistics
export const getUserStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const users = await ctx.db.query("users").collect();
    const adminUsers = await ctx.db.query("adminUsers").collect();
    const orders = await ctx.db.query("orders").collect();

    const activeAdmins = adminUsers.filter(admin => admin.isActive);
    const totalSpent = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const avgOrderValue = orders.length > 0 ? totalSpent / orders.length : 0;

    // Calculate user activity (users with orders in last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentOrders = orders.filter(order => order._creationTime > thirtyDaysAgo);
    const activeUsers = new Set(recentOrders.map(order => order.userId)).size;

    return {
      totalUsers: users.length,
      totalAdmins: adminUsers.length,
      activeAdmins: activeAdmins.length,
      activeUsers,
      totalOrders: orders.length,
      totalRevenue: totalSpent,
      avgOrderValue,
      userGrowth: {
        // This would need more complex logic to calculate growth rates
        // For now, just return basic stats
        thisMonth: users.filter(user => 
          user._creationTime > Date.now() - (30 * 24 * 60 * 60 * 1000)
        ).length,
      },
    };
  },
});

// Mutation to update user profile (limited fields)
export const updateUserProfile = mutation({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Validate email if being updated
    if (updates.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updates.email)) {
        throw new Error("Invalid email format");
      }

      // Check if email is already taken
      const existingUser = await ctx.db
        .query("users")
        .filter((q) => 
          q.and(
            q.eq(q.field("email"), updates.email),
            q.neq(q.field("_id"), id)
          )
        )
        .first();

      if (existingUser) {
        throw new Error("Email is already taken");
      }
    }

    // Validate name
    if (updates.name && !updates.name.trim()) {
      throw new Error("Name cannot be empty");
    }

    await ctx.db.patch(id, updates);

    return { success: true };
  },
});

// Mutation to deactivate a user (soft delete)
export const deactivateUser = mutation({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_DELETE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if user is an admin
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();

    if (adminUser) {
      // Deactivate admin privileges first
      await ctx.db.patch(adminUser._id, { isActive: false });
    }

    // For now, we'll just mark them as inactive in admin table
    // In a real app, you might want to add an isActive field to users table
    return { success: true };
  },
});
