import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { Doc } from "./_generated/dataModel";
import { orderStatsAggregate, orderCountAggregate, orderMonthlySalesAggregate } from "./aggregates";

// Query to get all orders with pagination and filtering
export const getOrders = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    status: v.optional(v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    )),
    assignedTo: v.optional(v.id("users")),
  },
  handler: async (ctx, { paginationOpts, status, assignedTo }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW);

    // Build query with proper initialization
    let query;

    // Apply filters
    if (status) {
      query = ctx.db.query("orders").withIndex("by_status", (q) => q.eq("status", status));
    } else if (assignedTo) {
      query = ctx.db.query("orders").withIndex("by_assigned_to", (q) => q.eq("assignedTo", assignedTo));
    } else {
      query = ctx.db.query("orders");
    }

    // Apply pagination
    if (paginationOpts) {
      const results = await query.paginate(paginationOpts);
      
      // Enrich orders with user and product information
      const enrichedOrders = await Promise.all(
        results.page.map(async (order) => {
          const user = await ctx.db.get(order.userId);
          const assignedUser = order.assignedTo ? await ctx.db.get(order.assignedTo) : null;
          
          // Get product details for each item
          const itemsWithDetails = await Promise.all(
            order.items.map(async (item) => {
              const product = await ctx.db.get(item.productId);
              return {
                ...item,
                product: product ? {
                  title: product.title,
                  images: product.images,
                  status: product.status,
                } : null,
              };
            })
          );

          return {
            ...order,
            user: user ? { 
              name: user.name, 
              email: user.email,
              id: user._id 
            } : null,
            assignedUser: assignedUser ? {
              name: assignedUser.name,
              email: assignedUser.email,
              id: assignedUser._id
            } : null,
            items: itemsWithDetails,
          };
        })
      );

      return {
        ...results,
        page: enrichedOrders,
      };
    }

    // Get all orders without pagination
    const orders = await query.collect();
    const enrichedOrders = await Promise.all(
      orders.map(async (order) => {
        const user = await ctx.db.get(order.userId);
        const assignedUser = order.assignedTo ? await ctx.db.get(order.assignedTo) : null;
        
        const itemsWithDetails = await Promise.all(
          order.items.map(async (item) => {
            const product = await ctx.db.get(item.productId);
            return {
              ...item,
              product: product ? {
                title: product.title,
                images: product.images,
                status: product.status,
              } : null,
            };
          })
        );

        return {
          ...order,
          user: user ? { 
            name: user.name, 
            email: user.email,
            id: user._id 
          } : null,
          assignedUser: assignedUser ? {
            name: assignedUser.name,
            email: assignedUser.email,
            id: assignedUser._id
          } : null,
          items: itemsWithDetails,
        };
      })
    );

    return enrichedOrders;
  },
});

// Query to get a single order by ID
export const getOrder = query({
  args: { id: v.id("orders") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW);

    const order = await ctx.db.get(id);
    if (!order) {
      throw new Error("Order not found");
    }

    const user = await ctx.db.get(order.userId);
    const assignedUser = order.assignedTo ? await ctx.db.get(order.assignedTo) : null;
    
    // Get detailed product information for each item
    const itemsWithDetails = await Promise.all(
      order.items.map(async (item) => {
        const product = await ctx.db.get(item.productId);
        const supplier = product ? await ctx.db.get(product.supplierId) : null;
        
        return {
          ...item,
          product: product ? {
            ...product,
            supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
          } : null,
        };
      })
    );

    return {
      ...order,
      user: user ? { 
        name: user.name, 
        email: user.email,
        id: user._id 
      } : null,
      assignedUser: assignedUser ? {
        name: assignedUser.name,
        email: assignedUser.email,
        id: assignedUser._id
      } : null,
      items: itemsWithDetails,
    };
  },
});

// Mutation to create a new order
export const createOrder = mutation({
  args: {
    userId: v.id("users"),
    items: v.array(v.object({
      productId: v.id("products"),
      quantity: v.number(),
      priceAtTime: v.number(),
      title: v.string(),
    })),
    shippingAddress: v.object({
      name: v.string(),
      address: v.string(),
      city: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    totalAmount: v.number(),
    providerOrderData: v.object({
      selectedVariant: v.optional(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceType: v.string(),
        finalPrice: v.number(),
      })),
      selectedQuantityTier: v.object({
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
      }),
      selectedCustomServices: v.array(v.object({
        name: v.string(),
        price: v.number(),
      })),
      totalProviderCost: v.number(),
      pricingBreakdown: v.object({
        basePrice: v.number(),
        variantAdjustment: v.number(),
        quantityDiscount: v.number(),
        customServicesTotal: v.number(),
        ourCommission: v.number(),
      }),
    }),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW); // Basic permission for order creation

    // Validate user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Validate all products exist and are available
    for (const item of args.items) {
      const product = await ctx.db.get(item.productId);
      if (!product) {
        throw new Error(`Product ${item.productId} not found`);
      }
      
      if (product.status !== "active") {
        throw new Error(`Product ${product.title} is not available`);
      }
      
      if (product.stockCount < item.quantity) {
        throw new Error(`Insufficient stock for ${product.title}`);
      }
    }

    // Validate input
    if (args.items.length === 0) {
      throw new Error("Order must contain at least one item");
    }

    if (args.totalAmount <= 0) {
      throw new Error("Total amount must be positive");
    }

    const orderId = await ctx.db.insert("orders", {
      ...args,
      status: "new",
      communicationHistory: [],
    });

    // Update aggregates
    const order = await ctx.db.get(orderId);
    if (order) {
      await orderStatsAggregate.insert(ctx, order);
      await orderCountAggregate.insert(ctx, order);
      await orderMonthlySalesAggregate.insert(ctx, order);
    }

    // Update product stock counts
    for (const item of args.items) {
      const product = await ctx.db.get(item.productId);
      if (product) {
        await ctx.db.patch(item.productId, {
          stockCount: product.stockCount - item.quantity,
        });
      }
    }

    return orderId;
  },
});

// Mutation to update order status
export const updateOrderStatus = mutation({
  args: {
    id: v.id("orders"),
    status: v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    issueResolution: v.optional(v.object({
      issue: v.string(),
      suggestedAlternatives: v.array(v.id("products")),
      resolution: v.optional(v.string()),
    })),
  },
  handler: async (ctx, { id, status, trackingNumber, issueResolution }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(id);
    if (!order) {
      throw new Error("Order not found");
    }

    const updates: Partial<Doc<"orders">> = { status };

    if (trackingNumber) {
      updates.trackingNumber = trackingNumber;
    }

    if (issueResolution) {
      updates.issueResolution = issueResolution;
    }

    await ctx.db.patch(id, updates);

    // Add status change to communication history
    const communicationEntry = {
      message: `Order status changed to ${status}`,
      fromAdmin: true,
      adminUserId: userId,
      timestamp: Date.now(),
    };

    await ctx.db.patch(id, {
      communicationHistory: [...order.communicationHistory, communicationEntry],
    });

    // Update aggregates
    const updatedOrder = await ctx.db.get(id);
    if (updatedOrder) {
      await orderStatsAggregate.replace(ctx, order, updatedOrder);
      // orderCountAggregate doesn't need update since count doesn't change
      await orderMonthlySalesAggregate.replace(ctx, order, updatedOrder);
    }

    return { success: true };
  },
});

// Mutation to assign order to admin user
export const assignOrder = mutation({
  args: {
    id: v.id("orders"),
    assignedTo: v.optional(v.id("users")),
  },
  handler: async (ctx, { id, assignedTo }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_ASSIGN);

    const order = await ctx.db.get(id);
    if (!order) {
      throw new Error("Order not found");
    }

    if (assignedTo) {
      // Verify the user exists and is an admin
      const adminUser = await ctx.db
        .query("adminUsers")
        .withIndex("by_user", (q) => q.eq("userId", assignedTo))
        .first();

      if (!adminUser || !adminUser.isActive) {
        throw new Error("Cannot assign to non-admin user");
      }
    }

    await ctx.db.patch(id, { assignedTo });

    return { success: true };
  },
});

// Mutation to add communication to order
export const addOrderCommunication = mutation({
  args: {
    id: v.id("orders"),
    message: v.string(),
  },
  handler: async (ctx, { id, message }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_COMMUNICATE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(id);
    if (!order) {
      throw new Error("Order not found");
    }

    if (!message.trim()) {
      throw new Error("Message cannot be empty");
    }

    const communicationEntry = {
      message: message.trim(),
      fromAdmin: true,
      adminUserId: userId,
      timestamp: Date.now(),
    };

    await ctx.db.patch(id, {
      communicationHistory: [...order.communicationHistory, communicationEntry],
    });

    return { success: true };
  },
});

// Query to get orders by user
export const getOrdersByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, { userId }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW);

    const orders = await ctx.db
      .query("orders")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    // Enrich with product details
    const enrichedOrders = await Promise.all(
      orders.map(async (order) => {
        const itemsWithDetails = await Promise.all(
          order.items.map(async (item) => {
            const product = await ctx.db.get(item.productId);
            return {
              ...item,
              product: product ? {
                title: product.title,
                images: product.images,
                status: product.status,
              } : null,
            };
          })
        );

        return {
          ...order,
          items: itemsWithDetails,
        };
      })
    );

    return enrichedOrders;
  },
});

// Query to get order statistics using efficient aggregates
export const getOrderStats = query({
  args: {
    startDate: v.optional(v.number()), // Unix timestamp
    endDate: v.optional(v.number()),   // Unix timestamp
  },
  handler: async (ctx, { startDate, endDate }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    let stats;
    let unassignedOrders;

    // If date range is provided, use direct database queries for filtering
    if (startDate && endDate) {
      const startTime = startDate;
      const endTime = endDate;

      // Query orders within the date range
      const ordersInRange = await ctx.db.query("orders")
        .filter(q => q.and(
          q.gte(q.field("_creationTime"), startTime),
          q.lt(q.field("_creationTime"), endTime)
        ))
        .collect();

      // Count orders by status
      stats = {
        total: ordersInRange.length,
        new: ordersInRange.filter(o => o.status === "new").length,
        sourcing: ordersInRange.filter(o => o.status === "sourcing").length,
        actionRequired: ordersInRange.filter(o => o.status === "action_required").length,
        shipped: ordersInRange.filter(o => o.status === "shipped").length,
        delivered: ordersInRange.filter(o => o.status === "delivered").length,
        cancelled: ordersInRange.filter(o => o.status === "cancelled").length,
        totalRevenue: ordersInRange
          .filter(o => o.status === "delivered")
          .reduce((sum, o) => sum + o.totalAmount, 0),
      };

      // Count unassigned orders in the date range
      unassignedOrders = ordersInRange.filter(o => !o.assignedTo);
    } else {
      // Use aggregates for O(log n) performance when no date filter
      stats = {
        total: await orderCountAggregate.count(ctx),
        new: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "new", inclusive: true }, upper: { key: "new", inclusive: true } }
        }),
        sourcing: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "sourcing", inclusive: true }, upper: { key: "sourcing", inclusive: true } }
        }),
        actionRequired: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "action_required", inclusive: true }, upper: { key: "action_required", inclusive: true } }
        }),
        shipped: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "shipped", inclusive: true }, upper: { key: "shipped", inclusive: true } }
        }),
        delivered: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "delivered", inclusive: true }, upper: { key: "delivered", inclusive: true } }
        }),
        cancelled: await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "cancelled", inclusive: true }, upper: { key: "cancelled", inclusive: true } }
        }),
        totalRevenue: await orderStatsAggregate.sum(ctx, {
          bounds: { lower: { key: "delivered", inclusive: true }, upper: { key: "delivered", inclusive: true } }
        }),
      };

      // For unassigned orders, we still need to query since it's not part of our aggregate key
      unassignedOrders = await ctx.db.query("orders")
        .filter(q => q.eq(q.field("assignedTo"), undefined))
        .collect();
    }

    return {
      ...stats,
      unassigned: unassignedOrders.length,
    };
  },
});
// Query to get monthly sales data using efficient aggregate with optional date filtering
export const getMonthlySales = query({
  args: {
    startDate: v.optional(v.number()), // Unix timestamp
    endDate: v.optional(v.number()),   // Unix timestamp
  },
  handler: async (ctx, { startDate, endDate }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const now = new Date();
    let startMonth, endMonth;

    // If date range is provided, use it; otherwise default to last 12 months
    if (startDate && endDate) {
      startMonth = new Date(startDate);
      endMonth = new Date(endDate);
    } else {
      // Default to last 12 months
      startMonth = new Date(now.getFullYear(), now.getMonth() - 11, 1);
      endMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0); // Last day of this month
    }

    const months = [];

    // Generate all months in the range
    for (let date = new Date(startMonth); date <= endMonth; date.setMonth(date.getMonth() + 1)) {
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthName = date.toLocaleString('en-US', { month: 'short', year: 'numeric' });

      const sales = await orderMonthlySalesAggregate.sum(ctx, {
        bounds: { lower: { key: monthKey, inclusive: true }, upper: { key: monthKey, inclusive: true } }
      });

      const orderCount = await orderMonthlySalesAggregate.count(ctx, {
        bounds: { lower: { key: monthKey, inclusive: true }, upper: { key: monthKey, inclusive: true } }
      });

      months.push({
        month: monthName,
        sales: sales || 0,
        orders: orderCount,
      });
    }

    return months;
  },
});
